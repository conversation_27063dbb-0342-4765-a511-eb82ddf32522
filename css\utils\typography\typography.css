/* Font Size Utilities */
.fs-xs { font-size: var(--mico-fs-xs) !important; }
.fs-sm { font-size: var(--mico-fs-sm) !important; }
.fs-md { font-size: var(--mico-fs-md) !important; }
.fs-lg { font-size: var(--mico-fs-lg) !important; }
.fs-xl { font-size: var(--mico-fs-xl) !important; }
.fs-ultra { font-size: var(--mico-fs-ultra) !important; }

/* Font Weight Utilities */
.fw-thin { font-weight: var(--mico-fw-thin) !important; }
.fw-extralight { font-weight: var(--mico-fw-extralight) !important; }
.fw-light { font-weight: var(--mico-fw-light) !important; }
.fw-normal { font-weight: var(--mico-fw-normal) !important; }
.fw-medium { font-weight: var(--mico-fw-medium) !important; }
.fw-semibold { font-weight: var(--mico-fw-semibold) !important; }
.fw-bold { font-weight: var(--mico-fw-bold) !important; }
.fw-extrabold { font-weight: var(--mico-fw-extrabold) !important; }
.fw-black { font-weight: var(--mico-fw-black) !important; }

/* Line Height Utilities */
.line-height-xs { line-height: var(--mico-line-height-xs) !important; }
.line-height-sm { line-height: var(--mico-line-height-sm) !important; }
.line-height-md { line-height: var(--mico-line-height-md) !important; }
.line-height-lg { line-height: var(--mico-line-height-lg) !important; }
.line-height-xl { line-height: var(--mico-line-height-xl) !important; }
.line-height-ultra { line-height: var(--mico-line-height-ultra) !important; }

/* Letter Spacing Utilities */
.letter-size-xs { letter-spacing: var(--mico-tracking-tighter) !important; }
.letter-size-sm { letter-spacing: var(--mico-tracking-tight) !important; }
.letter-size-md { letter-spacing: var(--mico-tracking-normal) !important; }
.letter-size-lg { letter-spacing: var(--mico-tracking-wide) !important; }
.letter-size-xl { letter-spacing: var(--mico-tracking-wider) !important; }
.letter-size-ultra { letter-spacing: var(--mico-tracking-widest) !important; }

/* Text Alignment Utilities */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Text Transform Utilities */
.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }
.normal-case { text-transform: none !important; }

/* Text Decoration Utilities */
.underline { 
  text-decoration: underline !important;
  text-underline-offset: var(--mico-underline-offset) !important;
  text-decoration-thickness: var(--mico-underline-thickness) !important;
}
.overline { text-decoration: overline !important; }
.line-through { text-decoration: line-through !important; }
.no-underline { text-decoration: none !important; }

/* Font Style Utilities */
.italic { font-style: italic !important; }
.not-italic { font-style: normal !important; }

/* Text Overflow Utilities */
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
.overflow-ellipsis { text-overflow: ellipsis !important; }
.text-clip { text-overflow: clip !important; }

/* Word Break Utilities */
.break-normal { overflow-wrap: normal !important; word-break: normal !important; }
.break-words { overflow-wrap: break-word !important ; }
.break-all { word-break: break-all !important; }

/* Whitespace Utilities */
.whitespace-normal { white-space: normal !important; }
.whitespace-nowrap { white-space: nowrap !important; }
.whitespace-pre { white-space: pre !important; }
.whitespace-pre-line { white-space: pre-line !important; }
.whitespace-pre-wrap { white-space: pre-wrap !important; }

/* Text Direction Utilities */
.text-ltr { direction: ltr !important; }
.text-rtl { direction: rtl !important; }

/* Vertical Alignment Utilities */
.align-baseline { vertical-align: baseline !important; }
.align-top { vertical-align: top !important; }
.align-middle { vertical-align: middle !important; }
.align-bottom { vertical-align: bottom !important; }
.align-text-top { vertical-align: text-top !important; }
.align-text-bottom { vertical-align: text-bottom !important; }

/* Font Variant Numeric Utilities */
.normal-nums { font-variant-numeric: normal; }
.ordinal { font-variant-numeric: ordinal; }
.slashed-zero { font-variant-numeric: slashed-zero; }
.lining-nums { font-variant-numeric: lining-nums; }
.oldstyle-nums { font-variant-numeric: oldstyle-nums; }
.proportional-nums { font-variant-numeric: proportional-nums; }
.tabular-nums { font-variant-numeric: tabular-nums; }
.diagonal-fractions { font-variant-numeric: diagonal-fractions; }
.stacked-fractions { font-variant-numeric: stacked-fractions; }

/* Text Columns Utilities */
.columns-1 { columns: 1; }
.columns-2 { columns: 2; }
.columns-3 { columns: 3; }
.columns-4 { columns: 4; }

/* Text Shadow Utilities */
.text-shadow-xs { text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1); }
.text-shadow-sm { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1); }
.text-shadow-md { text-shadow: 4px 4px 6px rgba(0, 0, 0, 0.1); }
.text-shadow-none { text-shadow: none; }

/* Font Smoothing Utilities */
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.subpixel-antialiased {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

/* Text Stroke Utilities */
.text-stroke-1 { -webkit-text-stroke-width: 1px; }
.text-stroke-2 { -webkit-text-stroke-width: 2px; }
.text-stroke-4 { -webkit-text-stroke-width: 4px; }

/* Writing Mode Utilities */
.writing-vertical-rl { writing-mode: vertical-rl; }
.writing-vertical-lr { writing-mode: vertical-lr; }
.writing-horizontal-tb { writing-mode: horizontal-tb; }

/* Text Indent Utilities */
.indent-sm { text-indent: 1em; }
.indent-md { text-indent: 2em; }
.indent-lg { text-indent: 3em; }

/* Hyphenation Utilities */
.hyphens-auto { hyphens: auto; }
.hyphens-manual { hyphens: manual; }
.hyphens-none { hyphens: none; }

/* Responsive Typography */
@media (min-width: 640px) {
  .sm\:text-left { text-align: left; }
  .sm\:text-center { text-align: center; }
  .sm\:text-right { text-align: right; }
  /* Add more responsive classes as needed */
}

@media (min-width: 768px) {
  .md\:text-left { text-align: left; }
  .md\:text-center { text-align: center; }
  .md\:text-right { text-align: right; }
  /* Add more responsive classes as needed */
}

@media (min-width: 1024px) {
  .lg\:text-left { text-align: left; }
  .lg\:text-center { text-align: center; }
  .lg\:text-right { text-align: right; }
  /* Add more responsive classes as needed */
}

@media (min-width: 1280px) {
  .xl\:text-left { text-align: left; }
  .xl\:text-center { text-align: center; }
  .xl\:text-right { text-align: right; }
  /* Add more responsive classes as needed */
}

/* Advanced Typography Features */
[data-drop-cap]::first-letter {
  float: left;
  font-size: 3em;
  line-height: 0.8;
  padding-right: 0.1em;
  padding-top: 0.1em;
}

[data-balance-text] {
  text-wrap: balance;
}

[data-pretty-quote]::before {
  content: open-quote;
}

[data-pretty-quote]::after {
  content: close-quote;
}

[data-gradient-text] {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}











