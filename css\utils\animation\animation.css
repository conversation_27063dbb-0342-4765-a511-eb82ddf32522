/* Transitions */
.transition-all { transition: var(--mico-transition-all); }
.transition-color { transition: var(--mico-transition-color); }
.transition-background { transition: var(--mico-transition-background); }
.transition-border { transition: var(--mico-transition-border); }
.transition-opacity { transition: var(--mico-transition-opacity); }
.transition-transform { transition: var(--mico-transition-transform); }
.transition-box-shadow { transition: var(--mico-transition-box-shadow); }
.transition-none {transition-property: none;}	


  /* Performance Optimizations */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
