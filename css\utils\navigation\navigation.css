/* navigation.utils.css */

/* Utility classes */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Header Navigation */
.header__nav {
  background-color: #333;
  color: #fff;
  padding: 1rem;
}

.nav__list {
  list-style: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__item {
  position: relative;
}

.nav__link {
  font-size: clamp(0.99rem, 1.4vw, 1.25rem) !important;
  text-decoration: none !important;
  transition: color 0.3s ease;
}

.nav__link:hover,
.nav__link:focus {
  color: currentColor;
}


/* Mobile Navigation */
.nav__toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.nav__hamburger,
.nav__hamburger::before,
.nav__hamburger::after {
  content: '';
  display: block;
  background-color: #d60c0c;
  height: 3px;
  width: 30px;
  transition: all 0.3s ease-in-out;
}

.nav__hamburger::before {
  transform: translateY(-8px);
}

.nav__hamburger::after {
  transform: translateY(5px);
}

/* Hamburger animation */
.nav__toggle[aria-expanded="true"] .nav__hamburger {
  background-color: transparent;
}

.nav__toggle[aria-expanded="true"] .nav__hamburger::before {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav__toggle[aria-expanded="true"] .nav__hamburger::after {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Footer Navigation */
.footer__nav {
  background-color: #f4f4f4;
  padding: 1rem;
}

.footer__nav .nav__list {
  flex-wrap: wrap;
  justify-content: center;
}

.footer__nav .nav__link {
  color: #333;
  font-size: 0.9rem;
}

/* Breadcrumbs */
.breadcrumbs {
  display: flex;
  list-style: none;
  padding: 1rem;
  background-color: #f0f0f0;
}

.breadcrumbs__item {
  display: flex;
  align-items: center;
}

.breadcrumbs__item:not(:last-child)::after {
  content: '/';
  margin: 0 0.5rem;
  color: #777;
}

.breadcrumbs__link {
  color: #333;
  text-decoration: none;
}

.breadcrumbs__link:hover,
.breadcrumbs__link:focus {
  text-decoration: underline;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 2rem 0;
}

.pagination__item {
  margin: 0 0.25rem;
}

.pagination__link {
  display: block;
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.pagination__link:hover,
.pagination__link:focus {
  background-color: #f0f0f0;
}

.pagination__link--active {
  background-color: #333;
  color: #fff;
  border-color: #333;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .header__nav .nav__list {
    display: none;
  }

  .nav__toggle {
    display: block;
  }

  .header__nav--open .nav__list {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #333;
    padding: 1rem;
  }

  .header__nav--open .nav__item {
    margin: 0.5rem 0;
  }

  /* Mobile navigation views */
  .nav__mobile--top {
    top: 0;
    left: 0;
    width: 100%;
  }

  .nav__mobile--left {
    top: 0;
    left: 0;
    height: 100vh;
    width: 80%;
    max-width: 300px;
    transform: translateX(-100%);
  }

  .nav__mobile--bottom {
    bottom: 0;
    left: 0;
    width: 100%;
  }

  .nav__mobile--right {
    top: 0;
    right: 0;
    height: 100vh;
    width: 80%;
    max-width: 300px;
    transform: translateX(100%);
  }

  .header__nav--open .nav__mobile--left,
  .header__nav--open .nav__mobile--right {
    transform: translateX(0);
  }

  /* Mobile navigation animation */
  .header__nav .nav__list {
    transition: transform 0.3s ease-in-out;
  }

  .nav__mobile--top,
  .nav__mobile--bottom {
    transition: transform 0.3s ease-in-out;
  }

  .nav__mobile--left,
  .nav__mobile--right {
    transition: transform 0.3s ease-in-out;
  }
}

/* Accessibility improvements */
.nav__link:focus-visible,
.nav__toggle:focus-visible,
.breadcrumbs__link:focus-visible,
.pagination__link:focus-visible {
  /* outline: 2px solid #ffd700; */
  outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background-color: #333;
  color: #fff;
  padding: 8px;
  z-index: 100;
}

.skip-link:focus {
  top: 0;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .header__nav,
  .footer__nav {
    background-color: #000;
    color: #fff;
  }

  .nav__link,
  .footer__nav .nav__link {
    color: #fff;
  }

  .nav__link:hover,
  .nav__link:focus,
  .footer__nav .nav__link:hover,
  .footer__nav .nav__link:focus {
    color: currentColor;
    text-decoration: underline;
  }

  .breadcrumbs {
    background-color: #fff;
    border: 1px solid #000;
  }

  .breadcrumbs__link,
  .pagination__link {
    color: #000;
    border-color: #000;
  }

  .pagination__link--active {
    background-color: #000;
    color: #fff;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
