/**
 * Mico CSS Framework - Interactive States
 *
 * This file defines utility classes for interactive states (hover, focus, active).
 * These classes follow a consistent naming pattern for easy use and extension.
 *
 * NAMING CONVENTION:
 * - Hover: .hover-{property}-{value}
 * - Focus: .focus-{property}-{value}
 * - Active: .active-{property}-{value}
 *
 * USAGE EXAMPLES:
 * <button class="hover-bg-primary">Hover for primary background</button>
 * <a class="hover-text-accent focus-text-primary">Interactive link</a>
 */

/* ========================================================================== */
/* BASE STATE UTILITIES                                                       */
/* ========================================================================== */

/**
 * Base transition for all interactive state classes
 * This ensures smooth transitions when states change
 */
[class^="hover-"], [class*=" hover-"],
[class^="focus-"], [class*=" focus-"],
[class^="active-"], [class*=" active-"] {
    transition: var(--mico-transition-all) !important;
}

/* ========================================================================== */
/* HOVER STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * BACKGROUND COLOR HOVER STATES
 *
 * These utilities apply background colors on hover
 */

/* ---------- Brand Colors ---------- */
.hover-bg-primary:hover { background-color: var(--mico-color-primary) !important; }
.hover-bg-secondary:hover { background-color: var(--mico-color-secondary) !important; }
.hover-bg-accent:hover { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Shades - Darker variations */
.hover-bg-primary-light:hover { background-color: var(--mico-color-primary-shade-light) !important; }
.hover-bg-primary-medium:hover { background-color: var(--mico-color-primary-shade-medium) !important; }
.hover-bg-primary-dark:hover { background-color: var(--mico-color-primary-shade-dark) !important; }
.hover-bg-primary-darker:hover { background-color: var(--mico-color-primary-shade-darker) !important; }
.hover-bg-primary-darkest:hover { background-color: var(--mico-color-primary-shade-darkest) !important; }

/* Tones - Lighter variations */
.hover-bg-primary-soft:hover { background-color: var(--mico-color-primary-tone-soft) !important; }
.hover-bg-primary-muted:hover { background-color: var(--mico-color-primary-tone-muted) !important; }
.hover-bg-primary-pastel:hover { background-color: var(--mico-color-primary-tone-pastel) !important; }
.hover-bg-primary-pale:hover { background-color: var(--mico-color-primary-tone-pale) !important; }
.hover-bg-primary-faint:hover { background-color: var(--mico-color-primary-tone-faint) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Shades - Darker variations */
.hover-bg-secondary-light:hover { background-color: var(--mico-color-secondary-shade-light) !important; }
.hover-bg-secondary-medium:hover { background-color: var(--mico-color-secondary-shade-medium) !important; }
.hover-bg-secondary-dark:hover { background-color: var(--mico-color-secondary-shade-dark) !important; }
.hover-bg-secondary-darker:hover { background-color: var(--mico-color-secondary-shade-darker) !important; }
.hover-bg-secondary-darkest:hover { background-color: var(--mico-color-secondary-shade-darkest) !important; }

/* Tones - Lighter variations */
.hover-bg-secondary-soft:hover { background-color: var(--mico-color-secondary-tone-soft) !important; }
.hover-bg-secondary-muted:hover { background-color: var(--mico-color-secondary-tone-muted) !important; }
.hover-bg-secondary-pastel:hover { background-color: var(--mico-color-secondary-tone-pastel) !important; }
.hover-bg-secondary-pale:hover { background-color: var(--mico-color-secondary-tone-pale) !important; }
.hover-bg-secondary-faint:hover { background-color: var(--mico-color-secondary-tone-faint) !important; }

/* ---------- Accent Color Variations ---------- */
/* Shades - Darker variations */
.hover-bg-accent-light:hover { background-color: var(--mico-color-accent-shade-light) !important; }
.hover-bg-accent-medium:hover { background-color: var(--mico-color-accent-shade-medium) !important; }
.hover-bg-accent-dark:hover { background-color: var(--mico-color-accent-shade-dark) !important; }
.hover-bg-accent-darker:hover { background-color: var(--mico-color-accent-shade-darker) !important; }
.hover-bg-accent-darkest:hover { background-color: var(--mico-color-accent-shade-darkest) !important; }

/* Tones - Lighter variations */
.hover-bg-accent-soft:hover { background-color: var(--mico-color-accent-tone-soft) !important; }
.hover-bg-accent-muted:hover { background-color: var(--mico-color-accent-tone-muted) !important; }
.hover-bg-accent-pastel:hover { background-color: var(--mico-color-accent-tone-pastel) !important; }
.hover-bg-accent-pale:hover { background-color: var(--mico-color-accent-tone-pale) !important; }
.hover-bg-accent-faint:hover { background-color: var(--mico-color-accent-tone-faint) !important; }

/* ---------- Grayscale ---------- */
.hover-bg-gray-100:hover { background-color: var(--mico-color-gray-100) !important; }
.hover-bg-gray-200:hover { background-color: var(--mico-color-gray-200) !important; }
.hover-bg-gray-300:hover { background-color: var(--mico-color-gray-300) !important; }
.hover-bg-gray-400:hover { background-color: var(--mico-color-gray-400) !important; }
.hover-bg-gray-500:hover { background-color: var(--mico-color-gray-500) !important; }
.hover-bg-gray-600:hover { background-color: var(--mico-color-gray-600) !important; }
.hover-bg-gray-700:hover { background-color: var(--mico-color-gray-700) !important; }
.hover-bg-gray-800:hover { background-color: var(--mico-color-gray-800) !important; }
.hover-bg-gray-900:hover { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.hover-bg-black-100:hover { background-color: var(--mico-color-black-100) !important; }
.hover-bg-black-200:hover { background-color: var(--mico-color-black-200) !important; }
.hover-bg-black-300:hover { background-color: var(--mico-color-black-300) !important; }
.hover-bg-black-400:hover { background-color: var(--mico-color-black-400) !important; }
.hover-bg-black-500:hover { background-color: var(--mico-color-black-500) !important; }
.hover-bg-black-600:hover { background-color: var(--mico-color-black-600) !important; }
.hover-bg-black-700:hover { background-color: var(--mico-color-black-700) !important; }
.hover-bg-black-800:hover { background-color: var(--mico-color-black-800) !important; }
.hover-bg-black-900:hover { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.hover-bg-black-trans-100:hover { background-color: var(--mico-color-black-trans-100) !important; }
.hover-bg-black-trans-200:hover { background-color: var(--mico-color-black-trans-200) !important; }
.hover-bg-black-trans-300:hover { background-color: var(--mico-color-black-trans-300) !important; }
.hover-bg-black-trans-400:hover { background-color: var(--mico-color-black-trans-400) !important; }
.hover-bg-black-trans-500:hover { background-color: var(--mico-color-black-trans-500) !important; }
.hover-bg-black-trans-600:hover { background-color: var(--mico-color-black-trans-600) !important; }
.hover-bg-black-trans-700:hover { background-color: var(--mico-color-black-trans-700) !important; }
.hover-bg-black-trans-800:hover { background-color: var(--mico-color-black-trans-800) !important; }
.hover-bg-black-trans-900:hover { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.hover-bg-success:hover { background-color: var(--mico-color-success) !important; }
.hover-bg-warning:hover { background-color: var(--mico-color-warning) !important; }
.hover-bg-error:hover { background-color: var(--mico-color-error) !important; }
.hover-bg-info:hover { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR HOVER STATES
 *
 * These utilities apply text colors on hover
 */
.hover-text-primary:hover { color: var(--mico-color-primary) !important; }
.hover-text-secondary:hover { color: var(--mico-color-secondary) !important; }
.hover-text-accent:hover { color: var(--mico-color-accent) !important; }


/* ========================================================================== */
/* FOCUS STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * BACKGROUND COLOR FOCUS STATES
 *
 * These utilities apply background colors on focus
 */

/* ---------- Brand Colors ---------- */
.focus-bg-primary:focus { background-color: var(--mico-color-primary) !important; }
.focus-bg-secondary:focus { background-color: var(--mico-color-secondary) !important; }
.focus-bg-accent:focus { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Shades - Darker variations */
.focus-bg-primary-light:focus { background-color: var(--mico-color-primary-shade-light) !important; }
.focus-bg-primary-medium:focus { background-color: var(--mico-color-primary-shade-medium) !important; }
.focus-bg-primary-dark:focus { background-color: var(--mico-color-primary-shade-dark) !important; }
.focus-bg-primary-darker:focus { background-color: var(--mico-color-primary-shade-darker) !important; }
.focus-bg-primary-darkest:focus { background-color: var(--mico-color-primary-shade-darkest) !important; }

/* Tones - Lighter variations */
.focus-bg-primary-soft:focus { background-color: var(--mico-color-primary-tone-soft) !important; }
.focus-bg-primary-muted:focus { background-color: var(--mico-color-primary-tone-muted) !important; }
.focus-bg-primary-pastel:focus { background-color: var(--mico-color-primary-tone-pastel) !important; }
.focus-bg-primary-pale:focus { background-color: var(--mico-color-primary-tone-pale) !important; }
.focus-bg-primary-faint:focus { background-color: var(--mico-color-primary-tone-faint) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Shades - Darker variations */
.focus-bg-secondary-light:focus { background-color: var(--mico-color-secondary-shade-light) !important; }
.focus-bg-secondary-medium:focus { background-color: var(--mico-color-secondary-shade-medium) !important; }
.focus-bg-secondary-dark:focus { background-color: var(--mico-color-secondary-shade-dark) !important; }
.focus-bg-secondary-darker:focus { background-color: var(--mico-color-secondary-shade-darker) !important; }
.focus-bg-secondary-darkest:focus { background-color: var(--mico-color-secondary-shade-darkest) !important; }

/* Tones - Lighter variations */
.focus-bg-secondary-soft:focus { background-color: var(--mico-color-secondary-tone-soft) !important; }
.focus-bg-secondary-muted:focus { background-color: var(--mico-color-secondary-tone-muted) !important; }
.focus-bg-secondary-pastel:focus { background-color: var(--mico-color-secondary-tone-pastel) !important; }
.focus-bg-secondary-pale:focus { background-color: var(--mico-color-secondary-tone-pale) !important; }
.focus-bg-secondary-faint:focus { background-color: var(--mico-color-secondary-tone-faint) !important; }

/* ---------- Accent Color Variations ---------- */
/* Shades - Darker variations */
.focus-bg-accent-light:focus { background-color: var(--mico-color-accent-shade-light) !important; }
.focus-bg-accent-medium:focus { background-color: var(--mico-color-accent-shade-medium) !important; }
.focus-bg-accent-dark:focus { background-color: var(--mico-color-accent-shade-dark) !important; }
.focus-bg-accent-darker:focus { background-color: var(--mico-color-accent-shade-darker) !important; }
.focus-bg-accent-darkest:focus { background-color: var(--mico-color-accent-shade-darkest) !important; }

/* Tones - Lighter variations */
.focus-bg-accent-soft:focus { background-color: var(--mico-color-accent-tone-soft) !important; }
.focus-bg-accent-muted:focus { background-color: var(--mico-color-accent-tone-muted) !important; }
.focus-bg-accent-pastel:focus { background-color: var(--mico-color-accent-tone-pastel) !important; }
.focus-bg-accent-pale:focus { background-color: var(--mico-color-accent-tone-pale) !important; }
.focus-bg-accent-faint:focus { background-color: var(--mico-color-accent-tone-faint) !important; }

/* ---------- Grayscale ---------- */
.focus-bg-gray-100:focus { background-color: var(--mico-color-gray-100) !important; }
.focus-bg-gray-200:focus { background-color: var(--mico-color-gray-200) !important; }
.focus-bg-gray-300:focus { background-color: var(--mico-color-gray-300) !important; }
.focus-bg-gray-400:focus { background-color: var(--mico-color-gray-400) !important; }
.focus-bg-gray-500:focus { background-color: var(--mico-color-gray-500) !important; }
.focus-bg-gray-600:focus { background-color: var(--mico-color-gray-600) !important; }
.focus-bg-gray-700:focus { background-color: var(--mico-color-gray-700) !important; }
.focus-bg-gray-800:focus { background-color: var(--mico-color-gray-800) !important; }
.focus-bg-gray-900:focus { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.focus-bg-black-100:focus { background-color: var(--mico-color-black-100) !important; }
.focus-bg-black-200:focus { background-color: var(--mico-color-black-200) !important; }
.focus-bg-black-300:focus { background-color: var(--mico-color-black-300) !important; }
.focus-bg-black-400:focus { background-color: var(--mico-color-black-400) !important; }
.focus-bg-black-500:focus { background-color: var(--mico-color-black-500) !important; }
.focus-bg-black-600:focus { background-color: var(--mico-color-black-600) !important; }
.focus-bg-black-700:focus { background-color: var(--mico-color-black-700) !important; }
.focus-bg-black-800:focus { background-color: var(--mico-color-black-800) !important; }
.focus-bg-black-900:focus { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.focus-bg-black-trans-100:focus { background-color: var(--mico-color-black-trans-100) !important; }
.focus-bg-black-trans-200:focus { background-color: var(--mico-color-black-trans-200) !important; }
.focus-bg-black-trans-300:focus { background-color: var(--mico-color-black-trans-300) !important; }
.focus-bg-black-trans-400:focus { background-color: var(--mico-color-black-trans-400) !important; }
.focus-bg-black-trans-500:focus { background-color: var(--mico-color-black-trans-500) !important; }
.focus-bg-black-trans-600:focus { background-color: var(--mico-color-black-trans-600) !important; }
.focus-bg-black-trans-700:focus { background-color: var(--mico-color-black-trans-700) !important; }
.focus-bg-black-trans-800:focus { background-color: var(--mico-color-black-trans-800) !important; }
.focus-bg-black-trans-900:focus { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.focus-bg-success:focus { background-color: var(--mico-color-success) !important; }
.focus-bg-warning:focus { background-color: var(--mico-color-warning) !important; }
.focus-bg-error:focus { background-color: var(--mico-color-error) !important; }
.focus-bg-info:focus { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR FOCUS STATES
 *
 * These utilities apply text colors on focus
 */
.focus-text-primary:focus { color: var(--mico-color-primary) !important; }
.focus-text-secondary:focus { color: var(--mico-color-secondary) !important; }
.focus-text-accent:focus { color: var(--mico-color-accent) !important; }

/* ========================================================================== */
/* ACTIVE STATE UTILITIES                                                     */
/* ========================================================================== */

/**
 * BACKGROUND COLOR ACTIVE STATES
 *
 * These utilities apply background colors on active (pressed) state
 */

/* ---------- Brand Colors ---------- */
.active-bg-primary:active { background-color: var(--mico-color-primary) !important; }
.active-bg-secondary:active { background-color: var(--mico-color-secondary) !important; }
.active-bg-accent:active { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Shades - Darker variations */
.active-bg-primary-light:active { background-color: var(--mico-color-primary-shade-light) !important; }
.active-bg-primary-medium:active { background-color: var(--mico-color-primary-shade-medium) !important; }
.active-bg-primary-dark:active { background-color: var(--mico-color-primary-shade-dark) !important; }
.active-bg-primary-darker:active { background-color: var(--mico-color-primary-shade-darker) !important; }
.active-bg-primary-darkest:active { background-color: var(--mico-color-primary-shade-darkest) !important; }

/* Tones - Lighter variations */
.active-bg-primary-soft:active { background-color: var(--mico-color-primary-tone-soft) !important; }
.active-bg-primary-muted:active { background-color: var(--mico-color-primary-tone-muted) !important; }
.active-bg-primary-pastel:active { background-color: var(--mico-color-primary-tone-pastel) !important; }
.active-bg-primary-pale:active { background-color: var(--mico-color-primary-tone-pale) !important; }
.active-bg-primary-faint:active { background-color: var(--mico-color-primary-tone-faint) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Shades - Darker variations */
.active-bg-secondary-light:active { background-color: var(--mico-color-secondary-shade-light) !important; }
.active-bg-secondary-medium:active { background-color: var(--mico-color-secondary-shade-medium) !important; }
.active-bg-secondary-dark:active { background-color: var(--mico-color-secondary-shade-dark) !important; }
.active-bg-secondary-darker:active { background-color: var(--mico-color-secondary-shade-darker) !important; }
.active-bg-secondary-darkest:active { background-color: var(--mico-color-secondary-shade-darkest) !important; }

/* Tones - Lighter variations */
.active-bg-secondary-soft:active { background-color: var(--mico-color-secondary-tone-soft) !important; }
.active-bg-secondary-muted:active { background-color: var(--mico-color-secondary-tone-muted) !important; }
.active-bg-secondary-pastel:active { background-color: var(--mico-color-secondary-tone-pastel) !important; }
.active-bg-secondary-pale:active { background-color: var(--mico-color-secondary-tone-pale) !important; }
.active-bg-secondary-faint:active { background-color: var(--mico-color-secondary-tone-faint) !important; }

/* ---------- Accent Color Variations ---------- */
/* Shades - Darker variations */
.active-bg-accent-light:active { background-color: var(--mico-color-accent-shade-light) !important; }
.active-bg-accent-medium:active { background-color: var(--mico-color-accent-shade-medium) !important; }
.active-bg-accent-dark:active { background-color: var(--mico-color-accent-shade-dark) !important; }
.active-bg-accent-darker:active { background-color: var(--mico-color-accent-shade-darker) !important; }
.active-bg-accent-darkest:active { background-color: var(--mico-color-accent-shade-darkest) !important; }

/* Tones - Lighter variations */
.active-bg-accent-soft:active { background-color: var(--mico-color-accent-tone-soft) !important; }
.active-bg-accent-muted:active { background-color: var(--mico-color-accent-tone-muted) !important; }
.active-bg-accent-pastel:active { background-color: var(--mico-color-accent-tone-pastel) !important; }
.active-bg-accent-pale:active { background-color: var(--mico-color-accent-tone-pale) !important; }
.active-bg-accent-faint:active { background-color: var(--mico-color-accent-tone-faint) !important; }

/* ---------- Grayscale ---------- */
.active-bg-gray-100:active { background-color: var(--mico-color-gray-100) !important; }
.active-bg-gray-200:active { background-color: var(--mico-color-gray-200) !important; }
.active-bg-gray-300:active { background-color: var(--mico-color-gray-300) !important; }
.active-bg-gray-400:active { background-color: var(--mico-color-gray-400) !important; }
.active-bg-gray-500:active { background-color: var(--mico-color-gray-500) !important; }
.active-bg-gray-600:active { background-color: var(--mico-color-gray-600) !important; }
.active-bg-gray-700:active { background-color: var(--mico-color-gray-700) !important; }
.active-bg-gray-800:active { background-color: var(--mico-color-gray-800) !important; }
.active-bg-gray-900:active { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.active-bg-black-100:active { background-color: var(--mico-color-black-100) !important; }
.active-bg-black-200:active { background-color: var(--mico-color-black-200) !important; }
.active-bg-black-300:active { background-color: var(--mico-color-black-300) !important; }
.active-bg-black-400:active { background-color: var(--mico-color-black-400) !important; }
.active-bg-black-500:active { background-color: var(--mico-color-black-500) !important; }
.active-bg-black-600:active { background-color: var(--mico-color-black-600) !important; }
.active-bg-black-700:active { background-color: var(--mico-color-black-700) !important; }
.active-bg-black-800:active { background-color: var(--mico-color-black-800) !important; }
.active-bg-black-900:active { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.active-bg-black-trans-100:active { background-color: var(--mico-color-black-trans-100) !important; }
.active-bg-black-trans-200:active { background-color: var(--mico-color-black-trans-200) !important; }
.active-bg-black-trans-300:active { background-color: var(--mico-color-black-trans-300) !important; }
.active-bg-black-trans-400:active { background-color: var(--mico-color-black-trans-400) !important; }
.active-bg-black-trans-500:active { background-color: var(--mico-color-black-trans-500) !important; }
.active-bg-black-trans-600:active { background-color: var(--mico-color-black-trans-600) !important; }
.active-bg-black-trans-700:active { background-color: var(--mico-color-black-trans-700) !important; }
.active-bg-black-trans-800:active { background-color: var(--mico-color-black-trans-800) !important; }
.active-bg-black-trans-900:active { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.active-bg-success:active { background-color: var(--mico-color-success) !important; }
.active-bg-warning:active { background-color: var(--mico-color-warning) !important; }
.active-bg-error:active { background-color: var(--mico-color-error) !important; }
.active-bg-info:active { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR ACTIVE STATES
 *
 * These utilities apply text colors on active state
 */
.active-text-primary:active { color: var(--mico-color-primary) !important; }
.active-text-secondary:active { color: var(--mico-color-secondary) !important; }
.active-text-accent:active { color: var(--mico-color-accent) !important; }

/* ========================================================================== */
/* ADDITIONAL INTERACTIVE UTILITIES                                           */
/* ========================================================================== */

/**
 * TRANSFORM HOVER EFFECTS
 *
 * These utilities apply transform effects on hover for enhanced interactivity
 */
.hover-scale:hover { transform: scale(1.05) !important; }
.hover-scale-lg:hover { transform: scale(1.1) !important; }
.hover-scale-sm:hover { transform: scale(1.025) !important; }
.hover-rotate-1:hover { transform: rotate(1deg) !important; }
.hover-rotate-2:hover { transform: rotate(2deg) !important; }
.hover-rotate-5:hover { transform: rotate(5deg) !important; }
.hover-translate-up-1:hover { transform: translateY(-1px) !important; }
.hover-translate-up-2:hover { transform: translateY(-2px) !important; }
.hover-translate-up-4:hover { transform: translateY(-4px) !important; }

/**
 * SHADOW HOVER EFFECTS
 *
 * These utilities apply shadow effects on hover
 */
.hover-shadow:hover { box-shadow: var(--mico-shadow-md) !important; }
.hover-shadow-lg:hover { box-shadow: var(--mico-shadow-lg) !important; }
.hover-shadow-sm:hover { box-shadow: var(--mico-shadow-sm) !important; }

/**
 * OPACITY HOVER EFFECTS
 *
 * These utilities apply opacity changes on hover
 */
.hover-opacity-75:hover { opacity: 0.75 !important; }
.hover-opacity-50:hover { opacity: 0.5 !important; }
.hover-opacity-100:hover { opacity: 1 !important; }

/**
 * FILTER HOVER EFFECTS
 *
 * These utilities apply filter effects on hover
 */
.hover-blur:hover { filter: blur(4px) !important; }
.hover-brightness-110:hover { filter: brightness(1.1) !important; }
.hover-brightness-125:hover { filter: brightness(1.25) !important; }
.hover-grayscale:hover { filter: grayscale(100%) !important; }
.hover-sepia:hover { filter: sepia(100%) !important; }

/* Complete hover effect for opacity, transforms*/

/* ========================================================================== */
/* USAGE EXAMPLES AND DOCUMENTATION                                           */
/* ========================================================================== */

/**
 * HOW TO USE INTERACTIVE STATE UTILITIES
 *
 * These utilities can be combined to create rich interactive experiences.
 * Here are some common patterns:
 *
 * 1. Basic hover effect for buttons:
 *    <button class="hover-bg-primary hover-text-white">Button</button>
 *
 * 2. Elevated card on hover:
 *    <div class="hover-elevate hover-bg-primary-pale">Card content</div>
 *
 * 3. Interactive link:
 *    <a class="hover-text-primary focus-text-primary-dark active-text-primary-darker">Link</a>
 *
 * 4. Subtle image hover effect:
 *    <img class="hover-brightness-110 hover-scale-sm" src="image.jpg" alt="Image">
 *
 * 5. Button with multiple states:
 *    <button class="hover-bg-primary focus-bg-primary-dark active-bg-primary-darker">Button</button>
 */

